#!/usr/bin/env python3
"""
Comprehensive test script for AdhocLog
Tests all new features including pagination, archive, and GUI launcher
"""

import sys
import os
import json
from pathlib import Path

def test_imports():
    """Test that all modules can be imported"""
    print("🔍 Testing imports...")
    try:
        from app import app
        from data_manager import DataManager
        from config import Config
        print("✅ All imports successful")
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def test_data_manager():
    """Test data manager functionality"""
    print("🔍 Testing data manager...")
    try:
        from data_manager import DataManager
        dm = DataManager()

        # Test filter_tasks with pagination
        result = dm.filter_tasks({}, page=1, per_page=5)
        assert 'tasks' in result, "Result should have 'tasks' key"
        assert 'pagination' in result, "Result should have 'pagination' key"

        # Test pagination structure
        pagination = result['pagination']
        required_keys = ['page', 'per_page', 'total', 'total_pages', 'has_prev', 'has_next']
        for key in required_keys:
            assert key in pagination, f"Pagination should have '{key}' key"

        # Test archived tasks pagination
        archived_result = dm.get_archived_tasks_paginated(page=1, per_page=5)
        assert 'tasks' in archived_result, "Archived result should have 'tasks' key"
        assert 'pagination' in archived_result, "Archived result should have 'pagination' key"

        print("✅ Data manager tests passed")
        return True
    except Exception as e:
        print(f"❌ Data manager error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_flask_routes():
    """Test Flask routes"""
    print("🔍 Testing Flask routes...")
    try:
        from app import app

        with app.test_client() as client:
            # Test dashboard
            response = client.get('/')
            assert response.status_code == 200, f"Dashboard should return 200, got {response.status_code}"

            # Test tasks page
            response = client.get('/tasks')
            assert response.status_code == 200, f"Tasks page should return 200, got {response.status_code}"

            # Test tasks page with pagination
            response = client.get('/tasks?page=1&per_page=5')
            assert response.status_code == 200, f"Tasks with pagination should return 200, got {response.status_code}"

            # Test add task page
            response = client.get('/tasks/add')
            assert response.status_code == 200, f"Add task page should return 200, got {response.status_code}"

            # Test archive page
            response = client.get('/archive')
            assert response.status_code == 200, f"Archive page should return 200, got {response.status_code}"

            # Test archive page with pagination
            response = client.get('/archive?page=1&per_page=5')
            assert response.status_code == 200, f"Archive with pagination should return 200, got {response.status_code}"

            # Test API endpoints
            response = client.get('/api/get_recent_tasks')
            assert response.status_code == 200, f"Recent tasks API should return 200, got {response.status_code}"

            # Test category API with new classifications
            classifications = [
                'Planning', 'Offline Processing', 'Execution',
                'Business Support Activities', 'Operational Project Involvement'
            ]

            for classification in classifications:
                response = client.get(f'/api/get_category/{classification}')
                assert response.status_code == 200, f"Category API for '{classification}' should return 200, got {response.status_code}"

                # Check response content
                data = response.get_json()
                assert 'category' in data, f"Category API should return category for '{classification}'"

            # Test export
            response = client.get('/export')
            assert response.status_code == 200, f"Export should return 200, got {response.status_code}"

        print("✅ Flask routes tests passed")
        return True
    except Exception as e:
        print(f"❌ Flask routes error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_templates():
    """Test that templates exist and are valid"""
    print("🔍 Testing templates...")
    try:
        template_files = [
            'templates/base.html',
            'templates/index.html',
            'templates/tasks.html',
            'templates/add_task.html',
            'templates/edit_task.html',
            'templates/archive.html',
            'templates/pagination.html'
        ]

        for template in template_files:
            path = Path(template)
            assert path.exists(), f"Template {template} should exist"

            # Basic validation - check if it's not empty
            content = path.read_text()
            assert len(content) > 0, f"Template {template} should not be empty"

        print("✅ Template tests passed")
        return True
    except Exception as e:
        print(f"❌ Template error: {e}")
        return False

def test_config():
    """Test configuration"""
    print("🔍 Testing configuration...")
    try:
        from config import Config
        config = Config()

        # Test new classifications
        expected_classifications = [
            'Planning', 'Offline Processing', 'Execution',
            'Business Support Activities', 'Operational Project Involvement'
        ]

        for classification in expected_classifications:
            assert classification in config.CLASSIFICATION_MAPPING, f"Classification '{classification}' should be in mapping"

        # Test category mappings
        expected_mappings = {
            'Planning': 'Adhoc',
            'Offline Processing': 'Adhoc',
            'Execution': 'Adhoc',
            'Business Support Activities': 'Business Support Activities',
            'Operational Project Involvement': 'Adhoc'
        }

        for classification, expected_category in expected_mappings.items():
            actual_category = config.CLASSIFICATION_MAPPING[classification]
            assert actual_category == expected_category, f"Classification '{classification}' should map to '{expected_category}', got '{actual_category}'"

        print("✅ Configuration tests passed")
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_gui_launcher():
    """Test GUI launcher imports"""
    print("🔍 Testing GUI launcher...")
    try:
        # Test that GUI launcher can be imported
        import gui_launcher

        # Test that required modules are available
        import tkinter

        print("✅ GUI launcher tests passed")
        return True
    except ImportError as e:
        print(f"⚠️  GUI launcher warning: {e} (This is OK if running headless)")
        return True
    except Exception as e:
        print(f"❌ GUI launcher error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 60)
    print("🧪 AdhocLog - Comprehensive Test Suite")
    print("=" * 60)

    tests = [
        test_imports,
        test_config,
        test_data_manager,
        test_flask_routes,
        test_templates,
        test_gui_launcher
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            print()

    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! The application is ready to use.")
        print()
        print("🚀 To start the application:")
        print("   • GUI: ./launch_app.sh (choose option 1)")
        print("   • Command line: ./launch_app.sh (choose option 2)")
        print("   • Windows: run.bat (choose option 1 or 2)")
        return 0
    else:
        print(f"❌ {total - passed} tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
