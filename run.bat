@echo off
echo ========================================
echo   Ad Hoc Task Tracker - Windows Launcher
echo ========================================

REM Change to the script's directory
cd /d "%~dp0"

REM Check for Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo.
    echo 📥 Please install Python 3.7+ from https://python.org/downloads
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

echo ✅ Python found:
python --version
echo.

:start
echo Choose how to launch the application:
echo.
echo 1. 🖥️  GUI Launcher (Recommended) - Easy graphical interface
echo 2. 🚀 Quick Start - Start app immediately
echo 3. ❓ Help
echo.
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo 🖥️ Starting GUI Launcher...
    python gui_launcher.py
) else if "%choice%"=="2" (
    echo 🚀 Quick Start - Starting application...
    python run.py
) else if "%choice%"=="3" (
    echo.
    echo ❓ Help & Information:
    echo.
    echo 📋 What this application does:
    echo • Track daily tasks and time spent
    echo • Organize tasks by classification
    echo • Export data to CSV for reporting
    echo • Archive completed tasks
    echo.
    echo 🖥️ GUI Launcher Features:
    echo • Easy point-and-click interface
    echo • Automatic setup and configuration
    echo • Built-in diagnostics and repair tools
    echo • Real-time status and logging
    echo.
    echo 📁 Files:
    echo • run.bat - This Windows launcher
    echo • gui_launcher.py - Graphical interface
    echo • launch_app.sh - Unix/Linux launcher
    echo • data\ - Your task data storage
    echo.
    echo 🆘 Troubleshooting:
    echo • If GUI doesn't work, try option 2
    echo • Check that Python 3.7+ is installed
    echo • Make sure you have internet connection for setup
    echo.
    pause
    goto start
) else (
    echo ❌ Invalid option. Please try again.
    echo.
    goto start
)
