import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')

    # Classification to Category mapping
    CLASSIFICATION_MAPPING = {
        'Planning': 'Adhoc',
        'Offline Processing': 'Adhoc',
        'Execution': 'Adhoc',
        'Business Support Activities': 'Business Support Activities',
        'Operational Project Involvement': 'Adhoc'
    }

    # Available classifications for dropdown
    CLASSIFICATIONS = list(CLASSIFICATION_MAPPING.keys())
