# 📋 AdhocLog

A simple, responsive Flask web application for tracking daily ad hoc tasks. Designed for team members to record, edit, delete, and export their daily work activities.

## ✨ Features

- **Task Management**: Add, edit, delete, and view tasks
- **Smart Classification**: Auto-mapping from classification to category
- **Filtering & Search**: Filter tasks by date, classification, or search terms
- **CSV Export**: Export filtered tasks for reporting
- **User Detection**: Automatically detects system user
- **Responsive Design**: Modern Bootstrap 5 interface
- **Local Storage**: JSON-based data storage per user
- **Offline Ready**: Works without internet connection

## 🚀 Quick Start

### 🖥️ GUI Launcher (Recommended for Everyone!)

**The Easiest Way - Graphical Interface:**

**Windows:** Double-click `run.bat` → Choose option 1
**macOS/Linux:** Run `./launch_app.sh` → Choose option 1

**Features:**
- ✅ **Point-and-click interface** - No command line needed!
- ✅ **Automatic setup** - Handles Python, dependencies, everything
- ✅ **Real-time progress** - See what's happening with progress bars
- ✅ **Built-in tools** - Diagnostics, repair, sample data creation
- ✅ **One-click start/stop** - Start and stop the app with buttons
- ✅ **Auto-open browser** - Opens your browser automatically
- ✅ **System information** - Shows your system details
- ✅ **Error handling** - Clear error messages and solutions

### 🚀 Quick Command Line (For Advanced Users)

**All Platforms:**
```bash
./launch_app.sh
```
Choose option 2 for immediate start, or option 3 for advanced utilities.

**Windows:**
```cmd
run.bat
```
Choose option 2 for quick start.

### 🎯 Perfect for Teams!

**Non-technical team members:** Use the GUI launcher - it's designed to be foolproof!
**System administrators:** Use command line options for automation and scripting.

### Manual Setup
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Create sample data (optional)
python create_sample_data.py

# Start application
python app.py
```

## 📁 Project Structure

```
adhoc-app/
├── app.py                 # Main Flask application
├── config.py              # Configuration settings
├── data_manager.py        # Data management layer
├── create_sample_data.py  # Sample data generator
├── requirements.txt       # Python dependencies
├── run.py                # Universal cross-platform launcher
├── run.bat               # Windows batch script
├── launch_app.sh         # Interactive launcher (macOS/Linux)
├── scripts/              # Utility scripts
│   ├── run.sh
│   ├── run_robust.sh
│   ├── run_debug.sh
│   ├── diagnose.sh
│   └── setup_missing_files.sh
├── data/                 # User data storage
│   ├── tasks_<username>.json
│   └── archived_tasks_<username>.json
├── static/               # Static assets
│   ├── css/
│   └── js/
└── templates/            # HTML templates
    ├── base.html
    ├── index.html
    ├── tasks.html
    ├── add_task.html
    ├── edit_task.html
    ├── archive.html
    ├── 404.html
    └── 500.html
```

## 🎯 Usage

### Dashboard
- View today's tasks and recent activity
- Quick stats on tasks and time spent
- Easy access to add new tasks

### Task Management
- **Add Task**: Fill out the form with title, classification, description, and estimated time
- **Quick Add**: Select from recent tasks to quickly create similar ones
- **Edit Task**: Click the edit button on any task to modify it
- **Archive Task**: Archive tasks instead of deleting (can be restored later)
- **Filter Tasks**: Use date ranges, classification, or search terms
- **Pagination**: Navigate through large task lists with page controls
- **Export Data**: Download filtered tasks as CSV for reporting

### Archive System
- **Archive Tasks**: Tasks are archived instead of permanently deleted
- **View Archive**: Access archived tasks from the Archive menu
- **Restore Tasks**: Bring archived tasks back to active status
- **Permanent Delete**: Permanently remove tasks from archive (cannot be undone)

### Classifications & Categories

| Classification | Auto-mapped Category |
|---------------|---------------------|
| Planning | Adhoc |
| Offline Processing | Adhoc |
| Execution | Adhoc |
| Business Support Activities | Business Support Activities |
| Operational Project Involvement | Adhoc |

## 🔧 Configuration

### Environment Variables
Create a `.env` file for custom configuration:
```bash
SECRET_KEY=your-secret-key-here
```

### Data Storage
- Data is stored in `data/tasks_<username>.json`
- Each user gets their own data file
- Automatic user detection via system username

## 🛠️ Development

### Adding New Classifications
Edit `config.py` and update the `CLASSIFICATION_MAPPING` dictionary:
```python
CLASSIFICATION_MAPPING = {
    'Bug Fix': 'Issue',
    'New Classification': 'New Category',
    # ... existing mappings
}
```

### Customizing the UI
- Templates are in the `templates/` folder
- Uses Bootstrap 5 for styling
- Custom CSS can be added to `static/css/`

## 📊 Data Format

Tasks are stored as JSON with the following structure:
```json
{
  "id": 1,
  "date": "2024-01-15",
  "team_member": "username",
  "title": "Task title",
  "classification": "Bug Fix",
  "category": "Issue",
  "description": "Task description",
  "est_time": 45
}
```

## 📋 Requirements

- Python 3.7 or higher
- Modern web browser
- No internet connection required (after initial setup)

## 🚨 Troubleshooting

### Quick Diagnosis
Run the diagnostic script to check your system:
```bash
./diagnose.sh
```

### Script Options
- `./run.sh` - Standard startup script
- `./run_robust.sh` - Enhanced script with better error handling and debugging
- `./diagnose.sh` - System diagnostic tool

### Common Issues

**Requirements installation fails**
```bash
# Try the robust script
./run_robust.sh

# Or manually:
python3 -m venv venv
source venv/bin/activate
python -m pip install --upgrade pip
python -m pip install -r requirements.txt
```

**Port 5000 already in use**
```bash
# Kill process using port 5000
# Windows:
netstat -ano | findstr :5000
taskkill /PID <PID> /F

# macOS/Linux:
lsof -ti:5000 | xargs kill -9
```

**Python not found**
- Ensure Python 3.7+ is installed
- Check PATH environment variable
- Try `python3` instead of `python`
- Use the robust script: `./run_robust.sh`

**Permission denied on run.sh**
```bash
chmod +x run.sh run_robust.sh diagnose.sh
```

**Virtual environment issues**
```bash
# Remove and recreate
rm -rf venv
./run_robust.sh
```

**pip not working**
```bash
# Upgrade pip first
python3 -m pip install --upgrade pip
# Then try again
python3 -m pip install -r requirements.txt
```

## 🔒 Security Notes

- Application runs locally only
- No external network access required
- Data stored locally per user
- No authentication required (single-user)

## 📝 License

This project is for internal use. Modify as needed for your organization.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Happy Task Tracking! 📋✨**
