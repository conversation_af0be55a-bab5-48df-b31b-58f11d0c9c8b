#!/usr/bin/env python3
"""
AdhocLog - GUI Launcher
User-friendly graphical interface for launching the application
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import subprocess
import threading
import sys
import os
import platform
import socket
import webbrowser
from pathlib import Path

class TaskTrackerLauncher:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("AdhocLog - Launcher")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # Variables
        self.process = None
        self.server_url = None

        # Setup UI
        self.setup_ui()

        # Center window
        self.center_window()

    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (800 // 2)
        y = (self.root.winfo_screenheight() // 2) - (600 // 2)
        self.root.geometry(f"800x600+{x}+{y}")

    def setup_ui(self):
        """Setup the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Title
        title_label = ttk.Label(main_frame, text="🚀 AdhocLog",
                               font=('Arial', 20, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        subtitle_label = ttk.Label(main_frame, text="Easy-to-use task tracking for IT Client Engineering Team",
                                  font=('Arial', 12))
        subtitle_label.grid(row=1, column=0, columnspan=2, pady=(0, 20))

        # System info frame
        info_frame = ttk.LabelFrame(main_frame, text="System Information", padding="10")
        info_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        info_frame.columnconfigure(1, weight=1)

        # System info
        ttk.Label(info_frame, text="Platform:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(info_frame, text=f"{platform.system()} {platform.release()}").grid(row=0, column=1, sticky=tk.W)

        ttk.Label(info_frame, text="Python:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(info_frame, text=f"{sys.version.split()[0]}").grid(row=1, column=1, sticky=tk.W)

        ttk.Label(info_frame, text="Directory:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        ttk.Label(info_frame, text=os.getcwd()).grid(row=2, column=1, sticky=tk.W)

        # Action buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.grid(row=3, column=0, columnspan=2, pady=(0, 20))

        # Main action buttons
        self.start_button = ttk.Button(buttons_frame, text="🚀 Start Application",
                                      command=self.start_application, style='Accent.TButton')
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(buttons_frame, text="🛑 Stop Application",
                                     command=self.stop_application, state='disabled')
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        self.browser_button = ttk.Button(buttons_frame, text="🌐 Open in Browser",
                                        command=self.open_browser, state='disabled')
        self.browser_button.pack(side=tk.LEFT, padx=(0, 10))

        # Utility buttons frame
        utils_frame = ttk.LabelFrame(main_frame, text="Utilities", padding="10")
        utils_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

        utils_buttons_frame = ttk.Frame(utils_frame)
        utils_buttons_frame.pack(fill=tk.X)

        ttk.Button(utils_buttons_frame, text="🔧 Setup/Repair",
                  command=self.run_setup).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(utils_buttons_frame, text="🩺 Diagnostics",
                  command=self.run_diagnostics).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(utils_buttons_frame, text="📊 Sample Data",
                  command=self.create_sample_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(utils_buttons_frame, text="📁 Open Data Folder",
                  command=self.open_data_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(utils_buttons_frame, text="🗑️ Clear Data",
                  command=self.clear_data).pack(side=tk.LEFT, padx=(0, 5))

        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="Status & Logs", padding="10")
        status_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)

        # Status label
        self.status_label = ttk.Label(status_frame, text="Ready to start", foreground="green")
        self.status_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 10))

        # Log text area
        self.log_text = scrolledtext.ScrolledText(status_frame, height=10, width=70)
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

        # Initial log message
        self.log("Welcome to AdhocLog!")
        self.log("Click 'Start Application' to begin.")
        self.log("=" * 50)

    def log(self, message):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def update_status(self, message, color="black"):
        """Update status label"""
        self.status_label.config(text=message, foreground=color)
        self.root.update_idletasks()

    def find_available_port(self, start_port=8000):
        """Find an available port"""
        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('127.0.0.1', port))
                    return port
            except OSError:
                continue
        return 5000  # fallback

    def start_application(self):
        """Start the Flask application"""
        def run_app():
            try:
                self.update_status("Starting application...", "orange")
                self.progress.start()
                self.start_button.config(state='disabled')

                self.log("🔍 Checking Python environment...")

                # Check if virtual environment exists
                venv_path = Path("venv")
                if not venv_path.exists():
                    self.log("📦 Creating virtual environment...")
                    subprocess.run([sys.executable, "-m", "venv", "venv"], check=True)
                    self.log("✅ Virtual environment created")

                # Get Python executable
                if platform.system() == "Windows":
                    python_exe = Path("venv/Scripts/python.exe")
                else:
                    python_exe = Path("venv/bin/python")

                # Install dependencies
                self.log("📥 Installing dependencies...")
                subprocess.run([str(python_exe), "-m", "pip", "install", "--upgrade", "pip"],
                              capture_output=True, check=True)
                subprocess.run([str(python_exe), "-m", "pip", "install", "-r", "requirements.txt"],
                              capture_output=True, check=True)
                self.log("✅ Dependencies installed")

                # Create sample data if needed
                data_dir = Path("data")
                if not data_dir.exists() or not any(data_dir.glob("tasks_*.json")):
                    self.log("📝 Creating sample data...")
                    subprocess.run([str(python_exe), "create_sample_data.py"],
                                  capture_output=True, check=True)
                    self.log("✅ Sample data created")

                # Find available port
                port = self.find_available_port()
                self.server_url = f"http://127.0.0.1:{port}"

                self.log(f"🚀 Starting server on port {port}...")

                # Start Flask app
                env = os.environ.copy()
                env['FLASK_RUN_PORT'] = str(port)

                # Create a startup script to ensure proper environment
                startup_script = f"""
import sys
import os
sys.path.insert(0, os.getcwd())
os.environ['FLASK_RUN_PORT'] = '{port}'

try:
    from app import app
    print(f'Starting Flask app on port {port}...')
    app.run(debug=False, host='0.0.0.0', port={port}, use_reloader=False)
except Exception as e:
    print(f'Error starting Flask app: {{e}}')
    import traceback
    traceback.print_exc()
"""

                self.process = subprocess.Popen([
                    str(python_exe), "-c", startup_script
                ], env=env, stdout=subprocess.PIPE, stderr=subprocess.STDOUT,
                    universal_newlines=True, bufsize=1, cwd=os.getcwd())

                self.update_status(f"Running on {self.server_url}", "green")
                self.stop_button.config(state='normal')
                self.browser_button.config(state='normal')
                self.progress.stop()

                self.log(f"✅ Application started successfully!")
                self.log(f"🌐 Open your browser to: {self.server_url}")
                self.log("🛑 Click 'Stop Application' to stop the server")

                # Auto-open browser
                webbrowser.open(self.server_url)

            except Exception as e:
                self.progress.stop()
                self.start_button.config(state='normal')
                self.update_status(f"Error: {str(e)}", "red")
                self.log(f"❌ Error starting application: {str(e)}")
                messagebox.showerror("Error", f"Failed to start application:\n{str(e)}")

        # Run in separate thread
        threading.Thread(target=run_app, daemon=True).start()

    def stop_application(self):
        """Stop the Flask application"""
        if self.process:
            self.log("🛑 Stopping application...")
            self.process.terminate()
            self.process = None
            self.update_status("Stopped", "red")
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')
            self.browser_button.config(state='disabled')
            self.log("✅ Application stopped")

    def open_browser(self):
        """Open the application in browser"""
        if self.server_url:
            webbrowser.open(self.server_url)
            self.log(f"🌐 Opened browser to {self.server_url}")

    def run_setup(self):
        """Run setup/repair utilities"""
        def setup():
            try:
                self.log("🔧 Running setup and repair...")
                self.progress.start()

                # Run setup script if it exists
                if Path("scripts/setup_missing_files.sh").exists():
                    result = subprocess.run(["bash", "scripts/setup_missing_files.sh"],
                                          capture_output=True, text=True)
                    self.log("✅ Setup completed")
                    if result.stdout:
                        self.log(result.stdout)
                else:
                    self.log("⚠️ Setup script not found, running basic setup...")
                    # Basic setup
                    subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"],
                                  capture_output=True)
                    subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
                                  capture_output=True)
                    self.log("✅ Basic setup completed")

                self.progress.stop()
                messagebox.showinfo("Setup Complete", "Setup and repair completed successfully!")

            except Exception as e:
                self.progress.stop()
                self.log(f"❌ Setup error: {str(e)}")
                messagebox.showerror("Setup Error", f"Setup failed:\n{str(e)}")

        threading.Thread(target=setup, daemon=True).start()

    def run_diagnostics(self):
        """Run system diagnostics"""
        def diagnose():
            try:
                self.log("🩺 Running diagnostics...")
                self.progress.start()

                # Check Python
                self.log(f"Python version: {sys.version}")

                # Check virtual environment
                venv_exists = Path("venv").exists()
                self.log(f"Virtual environment: {'✅ Found' if venv_exists else '❌ Missing'}")

                # Check requirements
                try:
                    import flask
                    self.log(f"Flask: ✅ {flask.__version__}")
                except ImportError:
                    self.log("Flask: ❌ Not installed")

                # Check data directory
                data_dir = Path("data")
                if data_dir.exists():
                    task_files = list(data_dir.glob("tasks_*.json"))
                    self.log(f"Data files: ✅ {len(task_files)} found")
                else:
                    self.log("Data directory: ❌ Missing")

                # Check ports
                port = self.find_available_port()
                self.log(f"Available port: ✅ {port}")

                self.progress.stop()
                self.log("🩺 Diagnostics completed")
                messagebox.showinfo("Diagnostics", "Diagnostics completed. Check logs for details.")

            except Exception as e:
                self.progress.stop()
                self.log(f"❌ Diagnostics error: {str(e)}")

        threading.Thread(target=diagnose, daemon=True).start()

    def create_sample_data(self):
        """Create sample data"""
        def create_data():
            try:
                self.log("📊 Creating sample data...")
                self.progress.start()

                # Get Python executable
                if platform.system() == "Windows":
                    python_exe = Path("venv/Scripts/python.exe")
                else:
                    python_exe = Path("venv/bin/python")

                if not python_exe.exists():
                    python_exe = sys.executable

                result = subprocess.run([str(python_exe), "create_sample_data.py"],
                                      capture_output=True, text=True)

                if result.returncode == 0:
                    self.log("✅ Sample data created successfully")
                    messagebox.showinfo("Success", "Sample data created successfully!")
                else:
                    self.log(f"❌ Error creating sample data: {result.stderr}")
                    messagebox.showerror("Error", f"Failed to create sample data:\n{result.stderr}")

                self.progress.stop()

            except Exception as e:
                self.progress.stop()
                self.log(f"❌ Error: {str(e)}")
                messagebox.showerror("Error", f"Failed to create sample data:\n{str(e)}")

        threading.Thread(target=create_data, daemon=True).start()

    def open_data_folder(self):
        """Open the data folder in file explorer"""
        data_dir = Path("data")
        if data_dir.exists():
            if platform.system() == "Windows":
                os.startfile(data_dir)
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", str(data_dir)])
            else:  # Linux
                subprocess.run(["xdg-open", str(data_dir)])
            self.log(f"📁 Opened data folder: {data_dir.absolute()}")
        else:
            messagebox.showwarning("Not Found", "Data folder does not exist yet.")

    def clear_data(self):
        """Clear all data files with confirmation"""
        data_path = Path("data")

        if not data_path.exists():
            messagebox.showinfo("Info", "No data folder exists yet.\nNothing to clear.")
            self.log("ℹ️ No data to clear")
            return

        # Count existing data files
        data_files = list(data_path.glob("tasks_*.json"))
        if not data_files:
            messagebox.showinfo("Info", "No data files found.\nNothing to clear.")
            self.log("ℹ️ No data files to clear")
            return

        # Show warning dialog
        warning_message = f"""⚠️ WARNING: This will permanently delete ALL your task data!

Found {len(data_files)} data file(s):
{chr(10).join([f"• {f.name}" for f in data_files[:5]])}
{'• ... and more' if len(data_files) > 5 else ''}

This action CANNOT be undone!

Are you sure you want to continue?"""

        result = messagebox.askyesno(
            "⚠️ Confirm Data Deletion",
            warning_message,
            icon='warning'
        )

        if not result:
            self.log("❌ Data clearing cancelled by user")
            return

        # Second confirmation for safety
        final_confirmation = messagebox.askyesno(
            "⚠️ Final Confirmation",
            "This is your FINAL WARNING!\n\nAll task data will be permanently deleted.\n\nProceed with deletion?",
            icon='warning'
        )

        if not final_confirmation:
            self.log("❌ Data clearing cancelled at final confirmation")
            return

        try:
            # Delete all data files
            deleted_count = 0
            for data_file in data_files:
                data_file.unlink()
                deleted_count += 1
                self.log(f"🗑️ Deleted: {data_file.name}")

            # Remove data directory if empty
            if not any(data_path.iterdir()):
                data_path.rmdir()
                self.log("🗑️ Removed empty data directory")

            messagebox.showinfo(
                "✅ Data Cleared",
                f"Successfully deleted {deleted_count} data file(s).\n\nThe application will create fresh sample data when you next run it."
            )
            self.log(f"✅ Successfully cleared {deleted_count} data files")

        except Exception as e:
            error_msg = f"Failed to clear data: {str(e)}"
            messagebox.showerror("Error", error_msg)
            self.log(f"❌ {error_msg}")

    def on_closing(self):
        """Handle window closing"""
        if self.process:
            if messagebox.askokcancel("Quit", "Application is running. Stop it and quit?"):
                self.stop_application()
                self.root.destroy()
        else:
            self.root.destroy()

    def run(self):
        """Run the GUI"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """Main function"""
    try:
        launcher = TaskTrackerLauncher()
        launcher.run()
    except Exception as e:
        messagebox.showerror("Error", f"Failed to start launcher:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
