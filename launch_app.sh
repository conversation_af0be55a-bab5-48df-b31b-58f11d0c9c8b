#!/bin/bash

# AdhocLog - Universal Launcher
# This script provides both GUI and command-line options

# Change to the script's directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "========================================"
echo "  🚀 AdhocLog"
echo "  Universal Launcher"
echo "========================================"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check for Python
PYTHON_CMD=""
PYTHON_VERSION=""

# Try to find Python
if command_exists python3; then
    PYTHON_CMD="python3"
    PYTHON_VERSION=$(python3 --version 2>/dev/null | cut -d' ' -f2)
elif command_exists python; then
    # Check if this is Python 3
    PYTHON_VERSION=$(python --version 2>/dev/null | cut -d' ' -f2)
    if [[ $PYTHON_VERSION == 3.* ]]; then
        PYTHON_CMD="python"
    fi
fi

# Function to attempt Python installation
install_python() {
    echo "🔄 Attempting to install Python..."

    case "$OSTYPE" in
        darwin*)
            # macOS
            if command_exists brew; then
                echo "📦 Installing Python via Homebrew..."
                brew install python3

                # Configure pip for potential SSL issues
                echo "🔧 Configuring pip for corporate/VPN environments..."
                mkdir -p ~/.pip
                cat > ~/.pip/pip.conf << EOF
[global]
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
EOF
            elif command_exists port; then
                echo "📦 Installing Python via MacPorts..."
                sudo port install python39
            else
                echo "❌ Please install Homebrew first: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
                echo "   Then run: brew install python3"
                return 1
            fi
            ;;
        linux*)
            # Linux
            if command_exists apt-get; then
                echo "📦 Installing Python via apt..."
                sudo apt-get update && sudo apt-get install -y python3 python3-pip python3-venv
            elif command_exists yum; then
                echo "📦 Installing Python via yum..."
                sudo yum install -y python3 python3-pip
            elif command_exists dnf; then
                echo "📦 Installing Python via dnf..."
                sudo dnf install -y python3 python3-pip
            elif command_exists pacman; then
                echo "📦 Installing Python via pacman..."
                sudo pacman -S python python-pip
            else
                echo "❌ Unsupported Linux distribution. Please install Python 3.7+ manually."
                return 1
            fi

            # Configure pip for potential SSL issues
            echo "🔧 Configuring pip for corporate/VPN environments..."
            mkdir -p ~/.pip
            cat > ~/.pip/pip.conf << EOF
[global]
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
EOF
            ;;
        msys*|cygwin*|mingw*)
            # Windows (Git Bash/MSYS/Cygwin)
            echo "❌ Automatic Python installation not supported on Windows."
            echo "📥 Please download and install Python from:"
            echo "   https://www.python.org/downloads/windows/"
            echo "   Make sure to check 'Add Python to PATH' during installation."
            return 1
            ;;
        *)
            echo "❌ Unsupported operating system: $OSTYPE"
            return 1
            ;;
    esac

    # Re-check for Python after installation
    if command_exists python3; then
        PYTHON_CMD="python3"
        PYTHON_VERSION=$(python3 --version 2>/dev/null | cut -d' ' -f2)
        echo "✅ Python installation successful!"
        return 0
    elif command_exists python; then
        PYTHON_VERSION=$(python --version 2>/dev/null | cut -d' ' -f2)
        if [[ $PYTHON_VERSION == 3.* ]]; then
            PYTHON_CMD="python"
            echo "✅ Python installation successful!"
            return 0
        fi
    fi

    return 1
}

if [ -z "$PYTHON_CMD" ]; then
    echo "❌ Python 3.7+ is required but not found!"
    echo
    read -p "Would you like to attempt automatic installation? (y/n): " install_choice

    if [[ $install_choice =~ ^[Yy]$ ]]; then
        if install_python; then
            echo "✅ Python installed successfully!"
        else
            echo
            echo "📥 Manual installation required. Please install Python 3.7 or higher:"
            echo "  • macOS: brew install python3"
            echo "  • Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
            echo "  • CentOS/RHEL: sudo yum install python3 python3-pip"
            echo "  • Fedora: sudo dnf install python3 python3-pip"
            echo "  • Windows: Download from https://python.org/downloads"
            echo "            (Make sure to check 'Add Python to PATH')"
            echo
            read -p "Press Enter to exit..."
            exit 1
        fi
    else
        echo
        echo "📥 Please install Python 3.7 or higher manually:"
        echo "  • macOS: brew install python3"
        echo "  • Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
        echo "  • CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "  • Fedora: sudo dnf install python3 python3-pip"
        echo "  • Windows: Download from https://python.org/downloads"
        echo "            (Make sure to check 'Add Python to PATH')"
        echo
        read -p "Press Enter to exit..."
        exit 1
    fi
fi

# Function to compare version numbers
version_compare() {
    # Returns 0 if $1 >= $2, 1 if $1 < $2
    local v1=$1
    local v2=$2

    # Extract major and minor version numbers
    local v1_major=$(echo $v1 | cut -d. -f1)
    local v1_minor=$(echo $v1 | cut -d. -f2)
    local v2_major=$(echo $v2 | cut -d. -f1)
    local v2_minor=$(echo $v2 | cut -d. -f2)

    # Compare major version
    if [ "$v1_major" -gt "$v2_major" ]; then
        return 0
    elif [ "$v1_major" -lt "$v2_major" ]; then
        return 1
    else
        # Major versions are equal, compare minor versions
        if [ "$v1_minor" -ge "$v2_minor" ]; then
            return 0
        else
            return 1
        fi
    fi
}

# Verify Python version
if ! version_compare "$PYTHON_VERSION" "3.7"; then
    echo "❌ Python $PYTHON_VERSION found, but 3.7+ is required!"
    echo "📥 Please upgrade Python to version 3.7 or higher"
    read -p "Press Enter to exit..."
    exit 1
fi

echo "✅ Found Python: $($PYTHON_CMD --version)"
echo

# Check if GUI is available (has display)
GUI_AVAILABLE=false
if [ -n "$DISPLAY" ] || [ "$OSTYPE" == "darwin"* ] || [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" ]]; then
    GUI_AVAILABLE=true
fi

# Show options
echo "Choose how to launch the application:"
echo
if [ "$GUI_AVAILABLE" = true ]; then
    echo "1. 🖥️  GUI Launcher (Recommended) - Easy graphical interface"
    echo "2. 🚀 Quick Start - Start app immediately"
    echo "3. 🔧 Advanced Options - Access utility scripts"
    echo "4. ❓ Help & Troubleshooting"
    echo
    read -p "Enter your choice (1-4): " choice
else
    echo "GUI not available. Using command-line options:"
    echo "1. 🚀 Quick Start - Start app immediately"
    echo "2. 🔧 Advanced Options - Access utility scripts"
    echo "3. ❓ Help & Troubleshooting"
    echo
    read -p "Enter your choice (1-3): " choice
    # Adjust choice for non-GUI systems
    case $choice in
        1) choice=2 ;;
        2) choice=3 ;;
        3) choice=4 ;;
    esac
fi

case $choice in
    1)
        if [ "$GUI_AVAILABLE" = true ]; then
            echo "🖥️ Starting GUI Launcher..."
            $PYTHON_CMD gui_launcher.py
        else
            echo "❌ GUI not available on this system"
            exit 1
        fi
        ;;
    2)
        echo "🚀 Quick Start - Starting application..."
        $PYTHON_CMD run.py
        ;;
    3)
        echo "🔧 Advanced Options:"
        echo "1. Setup/Repair Environment"
        echo "2. Run Diagnostics"
        echo "3. Create Sample Data"
        echo "4. Clear All Data (⚠️ Destructive)"
        echo "5. Debug Mode"
        echo "6. Back to Main Menu"
        echo
        read -p "Choose option (1-6): " adv_choice

        case $adv_choice in
            1)
                echo "🔧 Running setup..."
                if [ -f "scripts/setup_missing_files.sh" ]; then
                    bash scripts/setup_missing_files.sh
                else
                    echo "Setting up environment..."
                    $PYTHON_CMD -m venv venv 2>/dev/null || echo "Virtual env already exists"

                    # Activate virtual environment
                    if [ -f "venv/bin/activate" ]; then
                        source venv/bin/activate
                    elif [ -f "venv/Scripts/activate" ]; then
                        source venv/Scripts/activate
                    fi

                    echo "📦 Upgrading pip..."
                    # Try standard pip upgrade first
                    pip install --upgrade pip || {
                        echo "⚠️ Standard pip upgrade failed, trying with trusted hosts..."
                        pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --upgrade pip
                    }

                    echo "📦 Installing requirements..."
                    # Try standard requirements install first
                    pip install -r requirements.txt || {
                        echo "⚠️ Standard install failed, trying with trusted hosts (VPN/Corporate network workaround)..."
                        pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt
                    }

                    echo "✅ Setup complete"
                fi
                ;;
            2)
                echo "🩺 Running diagnostics..."
                if [ -f "scripts/diagnose.sh" ]; then
                    bash scripts/diagnose.sh
                else
                    echo "Python: $($PYTHON_CMD --version)"
                    echo "Virtual env: $([ -d "venv" ] && echo "✅ Found" || echo "❌ Missing")"
                    echo "Data dir: $([ -d "data" ] && echo "✅ Found" || echo "❌ Missing")"
                fi
                ;;
            3)
                echo "📊 Creating sample data..."
                $PYTHON_CMD create_sample_data.py
                echo "✅ Sample data created"
                ;;
            4)
                echo "🗑️ Clear All Data..."
                if [ -f "scripts/clear_data.sh" ]; then
                    bash scripts/clear_data.sh
                else
                    echo "⚠️ WARNING: This will delete all your task data!"
                    read -p "Are you sure? (yes/no): " confirm
                    if [[ $confirm == "yes" ]]; then
                        rm -f data/tasks_*.json 2>/dev/null
                        echo "✅ Data cleared"
                    else
                        echo "❌ Operation cancelled"
                    fi
                fi
                ;;
            5)
                echo "🐛 Starting in debug mode..."
                if [ -f "scripts/run_debug.sh" ]; then
                    bash scripts/run_debug.sh
                else
                    export FLASK_DEBUG=1
                    $PYTHON_CMD run.py
                fi
                ;;
            6)
                echo "↩️ Returning to main menu..."
                exec "$0"
                ;;
            *)
                echo "❌ Invalid option"
                ;;
        esac
        ;;
    4)
        echo "❓ Help & Troubleshooting:"
        echo
        echo "📋 Common Issues:"
        echo "• Python not found: Install Python 3.7+ from python.org"
        echo "• Permission denied: Run with appropriate permissions"
        echo "• Port in use: The app will find an available port automatically"
        echo "• Dependencies missing: Choose option 3 → 1 to run setup"
        echo
        echo "📁 File Structure:"
        echo "• launch_app.sh - This launcher script"
        echo "• gui_launcher.py - Graphical interface"
        echo "• run.py - Application starter"
        echo "• scripts/ - Utility scripts"
        echo "• data/ - Your task data"
        echo
        echo "🆘 Need Help?"
        echo "• Check the README.md file"
        echo "• Run diagnostics (option 3 → 2)"
        echo "• Contact your system administrator"
        echo
        read -p "Press Enter to return to main menu..."
        exec "$0"
        ;;
    *)
        echo "❌ Invalid option. Please try again."
        exec "$0"
        ;;
esac
