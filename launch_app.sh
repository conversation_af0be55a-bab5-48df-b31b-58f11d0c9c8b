#!/bin/bash

# AdhocLog - Universal Launcher
# This script provides both GUI and command-line options

# Change to the script's directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "========================================"
echo "  🚀 AdhocLog"
echo "  Universal Launcher"
echo "========================================"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to compare version numbers
version_compare() {
    # Returns 0 if $1 >= $2, 1 if $1 < $2
    local v1=$1
    local v2=$2

    # Extract major and minor version numbers
    local v1_major=$(echo $v1 | cut -d. -f1)
    local v1_minor=$(echo $v1 | cut -d. -f2)
    local v2_major=$(echo $v2 | cut -d. -f1)
    local v2_minor=$(echo $v2 | cut -d. -f2)

    # Compare major version
    if [ "$v1_major" -gt "$v2_major" ]; then
        return 0
    elif [ "$v1_major" -lt "$v2_major" ]; then
        return 1
    else
        # Major versions are equal, compare minor versions
        if [ "$v1_minor" -ge "$v2_minor" ]; then
            return 0
        else
            return 1
        fi
    fi
}

# Function to detect Python installations
detect_python() {
    local python_candidates=()
    local python_cmd=""
    local python_version=""
    local best_python=""
    local best_version=""

    # Common Python command names to try
    local python_names=("python3" "python" "python3.11" "python3.10" "python3.9" "python3.8" "python3.7")

    # Add Homebrew paths for macOS
    if [[ "$OSTYPE" == darwin* ]]; then
        # Intel Macs
        if [ -d "/usr/local/bin" ]; then
            for name in "${python_names[@]}"; do
                if [ -x "/usr/local/bin/$name" ]; then
                    python_candidates+=("/usr/local/bin/$name")
                fi
            done
        fi

        # Apple Silicon Macs
        if [ -d "/opt/homebrew/bin" ]; then
            for name in "${python_names[@]}"; do
                if [ -x "/opt/homebrew/bin/$name" ]; then
                    python_candidates+=("/opt/homebrew/bin/$name")
                fi
            done
        fi

        # pyenv installations
        if [ -d "$HOME/.pyenv/versions" ]; then
            for version_dir in "$HOME/.pyenv/versions"/*; do
                if [ -x "$version_dir/bin/python3" ]; then
                    python_candidates+=("$version_dir/bin/python3")
                fi
            done
        fi
    fi

    # Add system PATH candidates
    for name in "${python_names[@]}"; do
        if command_exists "$name"; then
            python_candidates+=("$name")
        fi
    done

    # Evaluate each candidate
    for candidate in "${python_candidates[@]}"; do
        if [ -x "$candidate" ] || command_exists "$candidate"; then
            version=$($candidate --version 2>/dev/null | cut -d' ' -f2)
            if [[ $version == 3.* ]]; then
                # Check if this version is better than our current best
                if [ -z "$best_python" ] || version_compare "$version" "$best_version"; then
                    # Test if tkinter is available (important for GUI)
                    if $candidate -c "import tkinter" 2>/dev/null; then
                        best_python="$candidate"
                        best_version="$version"
                        echo "✅ Found Python with tkinter: $candidate ($version)"
                    elif [ -z "$best_python" ]; then
                        # Use as fallback if no tkinter-enabled Python found yet
                        best_python="$candidate"
                        best_version="$version"
                        echo "⚠️ Found Python without tkinter: $candidate ($version)"
                    fi
                fi
            fi
        fi
    done

    if [ -n "$best_python" ]; then
        PYTHON_CMD="$best_python"
        PYTHON_VERSION="$best_version"
        return 0
    else
        return 1
    fi
}

# Check for Python
PYTHON_CMD=""
PYTHON_VERSION=""

echo "🔍 Detecting Python installations..."
if ! detect_python; then
    echo "❌ No suitable Python 3.7+ installation found!"
else
    echo "✅ Selected Python: $PYTHON_CMD ($PYTHON_VERSION)"

    # Check architecture on macOS
    if [[ "$OSTYPE" == darwin* ]]; then
        arch_info=$(uname -m)
        if [[ "$arch_info" == "arm64" ]]; then
            echo "🍎 Detected Apple Silicon Mac (ARM64)"
        else
            echo "🍎 Detected Intel Mac (x86_64)"
        fi

        # Check if we're using Homebrew Python and warn about potential issues
        if [[ "$PYTHON_CMD" == *"/opt/homebrew/"* ]]; then
            echo "🍺 Using Homebrew Python (Apple Silicon optimized)"
        elif [[ "$PYTHON_CMD" == *"/usr/local/"* ]]; then
            echo "🍺 Using Homebrew Python (Intel/Rosetta)"
        fi
    fi
fi

# Function to install tkinter on macOS
install_macos_tkinter() {
    echo "🔧 Installing tkinter for macOS..."

    if command_exists brew; then
        echo "📦 Installing python-tk via Homebrew..."
        brew install python-tk || {
            echo "⚠️ Homebrew python-tk install failed, trying alternative..."
            brew install python@3.11
        }
    else
        echo "❌ Homebrew not found. Please install it first:"
        echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        return 1
    fi

    # Test if tkinter works now
    if $PYTHON_CMD -c "import tkinter; print('tkinter available')" 2>/dev/null; then
        echo "✅ tkinter installation successful!"
        return 0
    else
        echo "⚠️ tkinter still not available. You may need to:"
        echo "   1. Use the official Python installer from python.org"
        echo "   2. Or try: brew reinstall python@3.11"
        return 1
    fi
}

# Function to configure pip for corporate/VPN environments
configure_pip() {
    echo "🔧 Configuring pip for corporate/VPN environments..."
    mkdir -p ~/.pip
    cat > ~/.pip/pip.conf << EOF
[global]
trusted-host = pypi.org
               pypi.python.org
               files.pythonhosted.org
timeout = 60
retries = 3
EOF

    # Also create pip.ini for Windows compatibility
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "mingw"* ]]; then
        mkdir -p ~/pip
        cp ~/.pip/pip.conf ~/pip/pip.ini
    fi
}

# Function to attempt Python installation
install_python() {
    echo "🔄 Attempting to install Python..."

    case "$OSTYPE" in
        darwin*)
            # macOS
            if command_exists brew; then
                echo "📦 Installing Python via Homebrew..."
                brew install python@3.11

                # Also install tkinter
                echo "📦 Installing tkinter support..."
                brew install python-tk

                configure_pip
            elif command_exists port; then
                echo "📦 Installing Python via MacPorts..."
                sudo port install python311 +tkinter
                configure_pip
            else
                echo "❌ Please install Homebrew first:"
                echo "   /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
                echo "   Then run this script again."
                return 1
            fi
            ;;
        linux*)
            # Linux
            if command_exists apt-get; then
                echo "📦 Installing Python via apt..."
                sudo apt-get update && sudo apt-get install -y python3 python3-pip python3-venv python3-tk
            elif command_exists yum; then
                echo "📦 Installing Python via yum..."
                sudo yum install -y python3 python3-pip python3-tkinter
            elif command_exists dnf; then
                echo "📦 Installing Python via dnf..."
                sudo dnf install -y python3 python3-pip python3-tkinter
            elif command_exists pacman; then
                echo "📦 Installing Python via pacman..."
                sudo pacman -S python python-pip tk
            else
                echo "❌ Unsupported Linux distribution. Please install Python 3.7+ manually."
                return 1
            fi

            configure_pip
            ;;
        msys*|cygwin*|mingw*)
            # Windows (Git Bash/MSYS/Cygwin)
            echo "❌ Automatic Python installation not supported on Windows."
            echo "📥 Please download and install Python from:"
            echo "   https://www.python.org/downloads/windows/"
            echo "   Make sure to check 'Add Python to PATH' during installation."
            configure_pip
            return 1
            ;;
        *)
            echo "❌ Unsupported operating system: $OSTYPE"
            return 1
            ;;
    esac

    # Re-check for Python after installation
    if command_exists python3; then
        PYTHON_CMD="python3"
        PYTHON_VERSION=$(python3 --version 2>/dev/null | cut -d' ' -f2)
        echo "✅ Python installation successful!"
        return 0
    elif command_exists python; then
        PYTHON_VERSION=$(python --version 2>/dev/null | cut -d' ' -f2)
        if [[ $PYTHON_VERSION == 3.* ]]; then
            PYTHON_CMD="python"
            echo "✅ Python installation successful!"
            return 0
        fi
    fi

    return 1
}

if [ -z "$PYTHON_CMD" ]; then
    echo "❌ Python 3.7+ is required but not found!"
    echo
    read -p "Would you like to attempt automatic installation? (y/n): " install_choice

    if [[ $install_choice =~ ^[Yy]$ ]]; then
        if install_python; then
            echo "✅ Python installed successfully!"
        else
            echo
            echo "📥 Manual installation required. Please install Python 3.7 or higher:"
            echo "  • macOS: brew install python3"
            echo "  • Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
            echo "  • CentOS/RHEL: sudo yum install python3 python3-pip"
            echo "  • Fedora: sudo dnf install python3 python3-pip"
            echo "  • Windows: Download from https://python.org/downloads"
            echo "            (Make sure to check 'Add Python to PATH')"
            echo
            read -p "Press Enter to exit..."
            exit 1
        fi
    else
        echo
        echo "📥 Please install Python 3.7 or higher manually:"
        echo "  • macOS: brew install python3"
        echo "  • Ubuntu/Debian: sudo apt install python3 python3-pip python3-venv"
        echo "  • CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "  • Fedora: sudo dnf install python3 python3-pip"
        echo "  • Windows: Download from https://python.org/downloads"
        echo "            (Make sure to check 'Add Python to PATH')"
        echo
        read -p "Press Enter to exit..."
        exit 1
    fi
fi



# Verify Python version
if ! version_compare "$PYTHON_VERSION" "3.7"; then
    echo "❌ Python $PYTHON_VERSION found, but 3.7+ is required!"
    echo "📥 Please upgrade Python to version 3.7 or higher"
    read -p "Press Enter to exit..."
    exit 1
fi

echo "✅ Found Python: $($PYTHON_CMD --version)"
echo

# Function to test tkinter functionality
test_tkinter() {
    local python_cmd="$1"
    local test_result

    # Try to import and create a test tkinter window
    test_result=$($python_cmd -c "
import sys
try:
    import tkinter as tk
    # Try to create a test window
    root = tk.Tk()
    root.withdraw()  # Hide the window
    root.destroy()
    print('tkinter_works')
    sys.exit(0)
except ImportError:
    print('tkinter_missing')
    sys.exit(1)
except Exception as e:
    print(f'tkinter_error:{e}')
    sys.exit(2)
" 2>/dev/null)

    case "$test_result" in
        "tkinter_works")
            return 0
            ;;
        "tkinter_missing")
            return 1
            ;;
        "tkinter_error:"*)
            return 2
            ;;
        *)
            return 3
            ;;
    esac
}

# Function to check if GUI is available
check_gui_available() {
    local gui_reason=""

    # First, test if tkinter actually works
    echo "🔍 Testing tkinter availability..."
    if test_tkinter "$PYTHON_CMD"; then
        echo "✅ tkinter is working correctly"
        GUI_REASON="tkinter available and functional"
        return 0
    else
        local tkinter_status=$?
        case $tkinter_status in
            1)
                echo "❌ tkinter is not installed"
                GUI_REASON="tkinter not installed"
                ;;
            2)
                echo "⚠️ tkinter is installed but not working (display issue)"
                GUI_REASON="tkinter installed but display unavailable"
                ;;
            *)
                echo "❌ tkinter test failed"
                GUI_REASON="tkinter test failed"
                ;;
        esac
    fi

    # Check environment-specific conditions
    if [ -n "$DISPLAY" ]; then
        echo "🖥️ X11/Wayland display detected: $DISPLAY"
        if [[ "$GUI_REASON" == "tkinter installed but display unavailable" ]]; then
            echo "💡 Display available but tkinter can't use it"
            return 1
        fi
    fi

    # Check for macOS
    if [[ "$OSTYPE" == darwin* ]]; then
        if [ -n "$SSH_CLIENT" ] || [ -n "$SSH_TTY" ]; then
            echo "🔒 SSH session detected - GUI not available"
            GUI_REASON="SSH session (no GUI access)"
            return 1
        fi

        # Check if we're in a headless environment
        if [ -z "$TERM_PROGRAM" ] && [ -z "$DISPLAY" ] && [ "$TERM" = "dumb" ]; then
            echo "🤖 Headless environment detected"
            GUI_REASON="headless environment"
            return 1
        fi

        # On macOS, if tkinter failed, suggest solutions
        if [[ "$GUI_REASON" == "tkinter not installed" ]]; then
            echo "💡 On macOS, try: brew install python-tk"
            return 1
        fi
    fi

    # Check for Windows environments
    if [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "mingw"* ]]; then
        if [[ "$GUI_REASON" == "tkinter not installed" ]]; then
            echo "💡 On Windows, reinstall Python with tkinter support"
            return 1
        fi
        # Assume GUI available on Windows if tkinter works
        return 0
    fi

    # For Linux, check more conditions
    if [[ "$OSTYPE" == linux* ]]; then
        if [ -z "$DISPLAY" ] && [ -z "$WAYLAND_DISPLAY" ]; then
            echo "🐧 Linux headless environment (no DISPLAY or WAYLAND_DISPLAY)"
            GUI_REASON="Linux headless environment"
            return 1
        fi

        if [[ "$GUI_REASON" == "tkinter not installed" ]]; then
            echo "💡 On Linux, try: sudo apt-get install python3-tk (Ubuntu/Debian)"
            echo "💡 Or: sudo yum install tkinter (CentOS/RHEL)"
            echo "💡 Or: sudo dnf install python3-tkinter (Fedora)"
            return 1
        fi
    fi

    # If we get here, GUI is not available
    return 1
}

# Check if GUI is available
echo ""
echo "🔍 Checking GUI availability..."
GUI_AVAILABLE=false
GUI_REASON=""

if check_gui_available; then
    GUI_AVAILABLE=true
    echo "✅ GUI is available!"
else
    echo "❌ GUI is not available"
    if [ -n "$GUI_REASON" ]; then
        echo "   Reason: $GUI_REASON"
    fi
fi

echo ""

# Show options
echo "Choose how to launch the application:"
echo
if [ "$GUI_AVAILABLE" = true ]; then
    echo "1. 🖥️  GUI Launcher (Recommended) - Easy graphical interface"
    echo "2. 🚀 Quick Start - Start app immediately"
    echo "3. 🔧 Advanced Options - Access utility scripts"
    echo "4. ❓ Help & Troubleshooting"
    echo
    read -p "Enter your choice (1-4): " choice
else
    echo "🖥️ GUI Launcher not available ($GUI_REASON)"
    echo "Available options:"
    echo "1. 🚀 Quick Start - Start app immediately"
    echo "2. 🔧 Advanced Options - Access utility scripts"
    echo "3. ❓ Help & Troubleshooting"
    echo
    read -p "Enter your choice (1-3): " choice
    # Adjust choice for non-GUI systems
    case $choice in
        1) choice=2 ;;
        2) choice=3 ;;
        3) choice=4 ;;
    esac
fi

case $choice in
    1)
        if [ "$GUI_AVAILABLE" = true ]; then
            echo "🖥️ Starting GUI Launcher..."
            $PYTHON_CMD gui_launcher.py
        else
            echo "❌ GUI not available on this system"
            exit 1
        fi
        ;;
    2)
        echo "🚀 Quick Start - Starting application..."
        $PYTHON_CMD run.py
        ;;
    3)
        echo "🔧 Advanced Options:"
        echo "1. Setup/Repair Environment"
        echo "2. Run Diagnostics"
        echo "3. Create Sample Data"
        echo "4. Clear All Data (⚠️ Destructive)"
        echo "5. Debug Mode"
        echo "6. Back to Main Menu"
        echo
        read -p "Choose option (1-6): " adv_choice

        case $adv_choice in
            1)
                echo "🔧 Running setup..."
                if [ -f "scripts/setup_missing_files.sh" ]; then
                    bash scripts/setup_missing_files.sh
                else
                    echo "Setting up environment..."

                    # Create virtual environment
                    if [ ! -d "venv" ]; then
                        echo "📦 Creating virtual environment..."
                        $PYTHON_CMD -m venv venv || {
                            echo "❌ Failed to create virtual environment"
                            echo "💡 Try: $PYTHON_CMD -m pip install --user virtualenv"
                            exit 1
                        }
                    else
                        echo "✅ Virtual environment already exists"
                    fi

                    # Activate virtual environment
                    if [ -f "venv/bin/activate" ]; then
                        source venv/bin/activate
                        VENV_PYTHON="venv/bin/python"
                    elif [ -f "venv/Scripts/activate" ]; then
                        source venv/Scripts/activate
                        VENV_PYTHON="venv/Scripts/python.exe"
                    else
                        echo "❌ Could not find virtual environment activation script"
                        exit 1
                    fi

                    echo "📦 Upgrading pip..."
                    # Try multiple pip upgrade strategies
                    $VENV_PYTHON -m pip install --upgrade pip || {
                        echo "⚠️ Standard pip upgrade failed, trying with trusted hosts..."
                        $VENV_PYTHON -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org --upgrade pip || {
                            echo "⚠️ Trusted hosts upgrade failed, trying with --user flag..."
                            $VENV_PYTHON -m pip install --user --upgrade pip || {
                                echo "⚠️ All pip upgrade methods failed, continuing with existing pip..."
                            }
                        }
                    }

                    echo "📦 Installing requirements..."
                    # Try multiple installation strategies
                    $VENV_PYTHON -m pip install -r requirements.txt || {
                        echo "⚠️ Standard install failed, trying with trusted hosts..."
                        $VENV_PYTHON -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org -r requirements.txt || {
                            echo "⚠️ Trusted hosts install failed, trying individual packages..."
                            # Try installing packages individually
                            while IFS= read -r package; do
                                if [[ ! "$package" =~ ^[[:space:]]*# ]] && [[ -n "$package" ]]; then
                                    echo "📦 Installing $package..."
                                    $VENV_PYTHON -m pip install --trusted-host pypi.org --trusted-host pypi.python.org --trusted-host files.pythonhosted.org "$package" || {
                                        echo "⚠️ Failed to install $package, continuing..."
                                    }
                                fi
                            done < requirements.txt
                        }
                    }

                    # Check for tkinter on macOS
                    if [[ "$OSTYPE" == darwin* ]]; then
                        echo "🔍 Checking tkinter availability..."
                        if ! $VENV_PYTHON -c "import tkinter; print('tkinter available')" 2>/dev/null; then
                            echo "⚠️ tkinter not available, attempting to install..."
                            install_macos_tkinter
                        else
                            echo "✅ tkinter is available"
                        fi
                    fi

                    echo "✅ Setup complete"
                fi
                ;;
            2)
                echo "🩺 Running diagnostics..."
                if [ -f "scripts/diagnose.sh" ]; then
                    bash scripts/diagnose.sh
                else
                    echo "Python: $($PYTHON_CMD --version)"
                    echo "Virtual env: $([ -d "venv" ] && echo "✅ Found" || echo "❌ Missing")"
                    echo "Data dir: $([ -d "data" ] && echo "✅ Found" || echo "❌ Missing")"
                fi
                ;;
            3)
                echo "📊 Creating sample data..."
                $PYTHON_CMD create_sample_data.py
                echo "✅ Sample data created"
                ;;
            4)
                echo "🗑️ Clear All Data..."
                if [ -f "scripts/clear_data.sh" ]; then
                    bash scripts/clear_data.sh
                else
                    echo "⚠️ WARNING: This will delete all your task data!"
                    read -p "Are you sure? (yes/no): " confirm
                    if [[ $confirm == "yes" ]]; then
                        rm -f data/tasks_*.json 2>/dev/null
                        echo "✅ Data cleared"
                    else
                        echo "❌ Operation cancelled"
                    fi
                fi
                ;;
            5)
                echo "🐛 Starting in debug mode..."
                if [ -f "scripts/run_debug.sh" ]; then
                    bash scripts/run_debug.sh
                else
                    export FLASK_DEBUG=1
                    $PYTHON_CMD run.py
                fi
                ;;
            6)
                echo "↩️ Returning to main menu..."
                exec "$0"
                ;;
            *)
                echo "❌ Invalid option"
                ;;
        esac
        ;;
    4)
        echo "❓ Help & Troubleshooting:"
        echo
        echo "📋 Common Issues & Solutions:"
        echo
        echo "🐍 Python Issues:"
        echo "• Python not found:"
        echo "  - macOS: brew install python@3.11"
        echo "  - Linux: sudo apt-get install python3 python3-pip python3-venv"
        echo "  - Windows: Download from python.org (check 'Add to PATH')"
        echo
        echo "🖥️ GUI Issues:"
        echo "• tkinter not available:"
        echo "  - macOS: brew install python-tk"
        echo "  - Linux: sudo apt-get install python3-tk"
        echo "  - Windows: Reinstall Python with tkinter support"
        echo "• GUI launcher won't start:"
        echo "  - Check if you're in SSH session (use option 1 instead)"
        echo "  - Try: export TK_SILENCE_DEPRECATION=1"
        echo
        echo "📦 Dependency Issues:"
        echo "• pip install fails:"
        echo "  - Corporate network: Run setup (option 2 → 1)"
        echo "  - Permission denied: Use virtual environment"
        echo "  - SSL errors: Setup configures trusted hosts automatically"
        echo
        echo "🌐 Network Issues:"
        echo "• Port in use: App finds available port automatically"
        echo "• Can't access app: Try http://localhost:PORT instead of 127.0.0.1"
        echo "• Firewall blocking: Check system firewall settings"
        echo
        echo "📁 File Structure:"
        echo "• launch_app.sh - This launcher script"
        echo "• gui_launcher.py - Graphical interface"
        echo "• run.py - Command-line application starter"
        echo "• scripts/ - Utility scripts for maintenance"
        echo "• data/ - Your task data (JSON files)"
        echo "• venv/ - Virtual environment (auto-created)"
        echo
        echo "🔧 Quick Fixes:"
        echo "• Run diagnostics: Choose option 2 → 2"
        echo "• Reset environment: Delete 'venv' folder and run setup"
        echo "• Clear data: Choose option 2 → 4 (⚠️ destructive)"
        echo
        echo "🆘 Still Need Help?"
        echo "• Check the README.md file for detailed instructions"
        echo "• Run full diagnostics to identify specific issues"
        echo "• Contact your system administrator with diagnostic output"
        echo
        read -p "Press Enter to return to main menu..."
        exec "$0"
        ;;
    *)
        echo "❌ Invalid option. Please try again."
        exec "$0"
        ;;
esac
