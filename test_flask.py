#!/usr/bin/env python3

"""
Simple Flask test to check if Flask is working
"""

try:
    from flask import Flask
    print("✅ Flask import successful")

    app = Flask(__name__)

    @app.route('/')
    def hello():
        return '''
        <h1>🎉 Flask is working!</h1>
        <p>If you can see this page, Flask is running correctly.</p>
        <p>You can now try running the main application.</p>
        '''

    @app.route('/test')
    def test():
        return {'status': 'success', 'message': 'Flask API is working'}

    if __name__ == '__main__':
        print("Starting simple Flask test server...")
        print("Open your browser to: http://127.0.0.1:5001")
        print("Press Ctrl+C to stop")
        print("-" * 40)

        app.run(debug=True, host='0.0.0.0', port=5001)

except ImportError as e:
    print(f"❌ Flask import failed: {e}")
    print("Please install Flask: pip install Flask")
except Exception as e:
    print(f"❌ Error: {e}")
