# AdhocLog - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# Flask
instance/
.webassets-cache

# Environment Variables
.env
.env.local
.env.production
.env.staging

# Data Files (User-specific data should not be in repo)
data/
*.json
!data/.gitkeep
!data/backup/.gitkeep

# Backup Files
data/backup/*
!data/backup/.gitkeep

# Logs
*.log
logs/

# IDE / Editor Files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# OS Files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# Temporary Files
*.tmp
*.temp
*.bak
*.backup

# Coverage Reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Node.js (if using any npm packages)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# SQLite databases (if using SQLite)
*.db
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old

# Archive files
*.zip
*.tar.gz
*.rar

# Application specific excludes
# Exclude user data but keep sample data structure
!data/sample_data.json

# Keep important empty directories
!static/css/.gitkeep
!static/js/.gitkeep
!static/images/.gitkeep
