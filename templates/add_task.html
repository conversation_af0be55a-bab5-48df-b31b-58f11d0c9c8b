{% extends "base.html" %}

{% block title %}Add Task - AdhocLog{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-plus-circle"></i> Add New Task</h1>
            <a href="{{ url_for('task_list') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Tasks
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Main Form -->
    <div class="col-lg-8">
        <!-- Task Form -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-form"></i> Task Details</h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    <div class="row g-3">
                        <!-- Title -->
                        <div class="col-12">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title"
                                   value="{{ task_data.get('title', '') }}" required>
                            <div class="invalid-feedback">
                                Please provide a task title.
                            </div>
                        </div>

                        <!-- Date -->
                        <div class="col-md-6">
                            <label for="date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="date" name="date"
                                   value="{{ task_data.get('date', '') }}">
                        </div>

                        <!-- Estimated Time -->
                        <div class="col-md-6">
                            <label for="est_time" class="form-label">Estimated Time (minutes)</label>
                            <input type="number" class="form-control" id="est_time" name="est_time"
                                   value="{{ task_data.get('est_time', 30) }}" min="1" max="999">
                        </div>

                        <!-- Classification -->
                        <div class="col-md-6">
                            <label for="classification" class="form-label">Classification <span class="text-danger">*</span></label>
                            <select class="form-select" id="classification" name="classification" required>
                                <option value="">Select Classification</option>
                                {% for cls in classifications %}
                                <option value="{{ cls }}" {% if task_data.get('classification') == cls %}selected{% endif %}>
                                    {{ cls }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Please select a classification.
                            </div>
                        </div>

                        <!-- Category (Auto-filled) -->
                        <div class="col-md-6">
                            <label for="category" class="form-label">Category</label>
                            <input type="text" class="form-control" id="category" name="category"
                                   value="{{ task_data.get('category', '') }}" readonly>
                            <div class="form-text">Auto-filled based on classification</div>
                        </div>

                        <!-- Description -->
                        <div class="col-12">
                            <label for="description" class="form-label">Description / Actions Taken</label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="Describe what you did or plan to do...">{{ task_data.get('description', '') }}</textarea>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Add Task
                            </button>
                            <a href="{{ url_for('task_list') }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle"></i> Cancel
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Quick Add from Previous Tasks -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Quick Add from Previous Tasks
                    <small class="text-muted ms-2">Click to auto-fill form</small>
                </h5>
            </div>
            <div class="card-body p-0">
                <div id="quickAddContainer" style="max-height: 300px; overflow-y: auto;">
                    <div class="text-center py-3">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <span class="ms-2 text-muted">Loading recent tasks...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Classification Guide -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Classification Guide</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Classification</th>
                                <th>Examples</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Planning</strong></td>
                                <td>
                                    <small>
                                        • Team meetings<br>
                                        • Brainstorming sessions<br>
                                        • Coordination with other teams
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Offline Processing</strong></td>
                                <td>
                                    <small>
                                        • Research and investigation<br>
                                        • Creating Excel reports<br>
                                        • Drafting documentation<br>
                                        • Preparing presentations<br>
                                        • Scripting/Coding<br>
                                        • Training/Certification
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Execution</strong></td>
                                <td>
                                    <small>
                                        • Presenting to managers<br>
                                        • Health checks<br>
                                        • Client System/Technology Operations<br>
                                        • Attendance/PTO Reminder<br>
                                        • Client Engineering Daily Case Management
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Business Support Activities</strong></td>
                                <td>
                                    <small>
                                        • Attend Townhall and other activities<br>
                                        • 1-on-1 Meeting/Catch-Up<br>
                                        • Daily Standup<br>
                                        • Technical Ramblings<br>
                                        • Client Engineering Team Weekly Meeting<br>
                                        • Audience of a Presentation
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Operational Project Involvement</strong></td>
                                <td>
                                    <small>
                                        • Assisting colleagues with difficult issues<br>
                                        • Participating in high-priority issue/SRT<br>
                                        • Operation Request from US team<br>
                                        • Involvement on US Projects<br>
                                        • Change Management
                                    </small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Global function for filling task form (needs to be outside DOMContentLoaded)
function fillTaskForm(taskDataJson) {
    const task = JSON.parse(decodeURIComponent(taskDataJson));

    // Fill form fields
    document.getElementById('title').value = task.title;
    document.getElementById('classification').value = task.classification;
    document.getElementById('category').value = task.category;
    document.getElementById('description').value = task.description;
    document.getElementById('est_time').value = task.est_time;

    // Keep today's date
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    document.getElementById('date').value = `${year}-${month}-${day}`;

    // Scroll to form
    document.getElementById('title').scrollIntoView({ behavior: 'smooth', block: 'center' });

    // Focus on title field for easy editing
    setTimeout(() => {
        document.getElementById('title').focus();
        document.getElementById('title').select();
    }, 500);
}

document.addEventListener('DOMContentLoaded', function() {
    // Set today's date as default
    const dateInput = document.getElementById('date');
    if (!dateInput.value) {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        dateInput.value = `${year}-${month}-${day}`;
    }

    // Load recent tasks for quick add
    loadRecentTasks();

    // Dynamic category mapping
    const classificationSelect = document.getElementById('classification');
    const categoryInput = document.getElementById('category');

    classificationSelect.addEventListener('change', function() {
        const classification = this.value;
        if (classification) {
            fetch(`/api/get_category/${encodeURIComponent(classification)}`)
                .then(response => response.json())
                .then(data => {
                    categoryInput.value = data.category;
                })
                .catch(error => {
                    console.error('Error fetching category:', error);
                    categoryInput.value = 'Other';
                });
        } else {
            categoryInput.value = '';
        }
    });

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Real-time validation feedback
    const requiredInputs = form.querySelectorAll('[required]');
    requiredInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });

    // Load recent tasks for quick add
    function loadRecentTasks() {
        fetch('/api/get_recent_tasks')
            .then(response => response.json())
            .then(data => {
                const container = document.getElementById('quickAddContainer');
                if (data.tasks && data.tasks.length > 0) {
                    let html = '<div class="list-group list-group-flush">';
                    data.tasks.forEach((task, index) => {
                        // Truncate long titles
                        const shortTitle = task.title.length > 50 ? task.title.substring(0, 50) + '...' : task.title;
                        const shortDesc = task.description.length > 80 ? task.description.substring(0, 80) + '...' : task.description;

                        html += `
                            <div class="list-group-item list-group-item-action py-2" style="cursor: pointer; border-left: 3px solid #0d6efd;" onclick="fillTaskForm('${encodeURIComponent(JSON.stringify(task))}')">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1" style="font-size: 0.9rem; font-weight: 600;">${shortTitle}</h6>
                                        <p class="mb-1 text-muted" style="font-size: 0.8rem;">${shortDesc}</p>
                                        <div class="d-flex gap-1">
                                            <span class="badge bg-secondary" style="font-size: 0.7rem;">${task.classification}</span>
                                            <span class="badge bg-info" style="font-size: 0.7rem;">${task.category}</span>
                                        </div>
                                    </div>
                                    <div class="text-end ms-2">
                                        <small class="text-muted d-block">${task.est_time} min</small>
                                        <i class="bi bi-arrow-right-circle text-primary"></i>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    html += '</div>';
                    container.innerHTML = html;
                } else {
                    container.innerHTML = '<div class="text-center py-3"><i class="bi bi-inbox text-muted"></i><p class="text-muted mt-2 mb-0">No recent tasks found.</p></div>';
                }
            })
            .catch(error => {
                console.error('Error loading recent tasks:', error);
                document.getElementById('quickAddContainer').innerHTML =
                    '<div class="text-center py-3"><i class="bi bi-exclamation-triangle text-warning"></i><p class="text-muted mt-2 mb-0">Error loading recent tasks.</p></div>';
            });
    }


});
</script>
{% endblock %}
