{% extends "base.html" %}

{% block title %}Archive - AdhocLog{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-archive"></i> Archived Tasks</h1>
            <a href="{{ url_for('task_list') }}" class="btn btn-outline-primary">
                <i class="bi bi-arrow-left"></i> Back to Active Tasks
            </a>
        </div>
    </div>
</div>

<!-- Archived Tasks Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-archive-fill"></i> Archived Tasks
                    <span class="badge bg-secondary">{{ archived_tasks|length }}</span>
                </h5>
            </div>
            <div class="card-body">
                {% if archived_tasks %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Title</th>
                                    <th>Classification</th>
                                    <th>Category</th>
                                    <th>Description</th>
                                    <th>Time (min)</th>
                                    <th>Archived</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in archived_tasks %}
                                <tr>
                                    <td>{{ task.date }}</td>
                                    <td><strong>{{ task.title }}</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">{{ task.classification }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ task.category }}</span>
                                    </td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;"
                                              title="{{ task.description }}">
                                            {{ task.description }}
                                        </span>
                                    </td>
                                    <td>{{ task.est_time }}</td>
                                    <td>
                                        <small class="text-muted">{{ task.archived_date }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-success"
                                                    onclick="confirmRestore({{ task.id }}, '{{ task.title }}')" title="Restore">
                                                <i class="bi bi-arrow-counterclockwise"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="confirmPermanentDelete({{ task.id }}, '{{ task.title }}')" title="Permanently Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% include 'pagination.html' %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-archive fs-1 text-muted"></i>
                        <p class="text-muted mt-2">No archived tasks found.</p>
                        <a href="{{ url_for('task_list') }}" class="btn btn-primary">
                            <i class="bi bi-list"></i> View Active Tasks
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Restore Confirmation Modal -->
<div class="modal fade" id="restoreModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Restore</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to restore the task "<span id="restoreTaskTitle"></span>"?</p>
                <p class="text-muted">This will move the task back to your active tasks list.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="restoreForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-arrow-counterclockwise"></i> Restore
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Permanent Delete Confirmation Modal -->
<div class="modal fade" id="permanentDeleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Permanent Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to <strong>permanently delete</strong> the task "<span id="deleteTaskTitle"></span>"?</p>
                <p class="text-danger"><strong>Warning:</strong> This action cannot be undone!</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="permanentDeleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> Permanently Delete
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmRestore(taskId, taskTitle) {
    document.getElementById('restoreTaskTitle').textContent = taskTitle;
    document.getElementById('restoreForm').action = '/tasks/restore/' + taskId;
    new bootstrap.Modal(document.getElementById('restoreModal')).show();
}

function confirmPermanentDelete(taskId, taskTitle) {
    document.getElementById('deleteTaskTitle').textContent = taskTitle;
    document.getElementById('permanentDeleteForm').action = '/tasks/permanent_delete/' + taskId;
    new bootstrap.Modal(document.getElementById('permanentDeleteModal')).show();
}
</script>
{% endblock %}
