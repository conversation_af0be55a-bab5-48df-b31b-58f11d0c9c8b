{% extends "base.html" %}

{% block title %}Edit Task - AdhocLog{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-pencil"></i> Edit Task</h1>
            <a href="{{ url_for('task_list') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> Back to Tasks
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Main Form -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-form"></i> Task Details</h5>
            </div>
            <div class="card-body">
                <form method="POST" novalidate>
                    <div class="row g-3">
                        <!-- Title -->
                        <div class="col-12">
                            <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="title" name="title"
                                   value="{{ task.get('title', '') }}" required>
                            <div class="invalid-feedback">
                                Please provide a task title.
                            </div>
                        </div>

                        <!-- Date -->
                        <div class="col-md-6">
                            <label for="date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="date" name="date"
                                   value="{{ task.get('date', '') }}">
                        </div>

                        <!-- Estimated Time -->
                        <div class="col-md-6">
                            <label for="est_time" class="form-label">Estimated Time (minutes)</label>
                            <input type="number" class="form-control" id="est_time" name="est_time"
                                   value="{{ task.get('est_time', 30) }}" min="1" max="999">
                        </div>

                        <!-- Classification -->
                        <div class="col-md-6">
                            <label for="classification" class="form-label">Classification <span class="text-danger">*</span></label>
                            <select class="form-select" id="classification" name="classification" required>
                                <option value="">Select Classification</option>
                                {% for cls in classifications %}
                                <option value="{{ cls }}" {% if task.get('classification') == cls %}selected{% endif %}>
                                    {{ cls }}
                                </option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                Please select a classification.
                            </div>
                        </div>

                        <!-- Category (Auto-filled) -->
                        <div class="col-md-6">
                            <label for="category" class="form-label">Category</label>
                            <input type="text" class="form-control" id="category" name="category"
                                   value="{{ task.get('category', '') }}" readonly>
                            <div class="form-text">Auto-filled based on classification</div>
                        </div>

                        <!-- Description -->
                        <div class="col-12">
                            <label for="description" class="form-label">Description / Actions Taken</label>
                            <textarea class="form-control" id="description" name="description" rows="4"
                                      placeholder="Describe what you did or plan to do...">{{ task.get('description', '') }}</textarea>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update Task
                            </button>
                            <a href="{{ url_for('task_list') }}" class="btn btn-secondary">
                                <i class="bi bi-x-circle"></i> Cancel
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Classification Guide -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle"></i> Classification Guide</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Classification</th>
                                <th>Examples</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Planning</strong></td>
                                <td>
                                    <small>
                                        • Team meetings<br>
                                        • Brainstorming sessions<br>
                                        • Coordination with other teams
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Offline Processing</strong></td>
                                <td>
                                    <small>
                                        • Research and investigation<br>
                                        • Creating Excel reports<br>
                                        • Drafting documentation<br>
                                        • Preparing presentations<br>
                                        • Scripting/Coding<br>
                                        • Training/Certification
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Execution</strong></td>
                                <td>
                                    <small>
                                        • Presenting to managers<br>
                                        • Health checks<br>
                                        • Client System/Technology Operations<br>
                                        • Attendance/PTO Reminder<br>
                                        • Client Engineering Daily Case Management
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Business Support Activities</strong></td>
                                <td>
                                    <small>
                                        • Attend Townhall and other activities<br>
                                        • 1-on-1 Meeting/Catch-Up<br>
                                        • Daily Standup<br>
                                        • Technical Ramblings<br>
                                        • Client Engineering Team Weekly Meeting<br>
                                        • Audience of a Presentation
                                    </small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Operational Project Involvement</strong></td>
                                <td>
                                    <small>
                                        • Assisting colleagues with difficult issues<br>
                                        • Participating in high-priority issue/SRT<br>
                                        • Operation Request from US team<br>
                                        • Involvement on US Projects<br>
                                        • Change Management
                                    </small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Dynamic category mapping
    const classificationSelect = document.getElementById('classification');
    const categoryInput = document.getElementById('category');

    classificationSelect.addEventListener('change', function() {
        const classification = this.value;
        if (classification) {
            fetch(`/api/get_category/${encodeURIComponent(classification)}`)
                .then(response => response.json())
                .then(data => {
                    categoryInput.value = data.category;
                })
                .catch(error => {
                    console.error('Error fetching category:', error);
                    categoryInput.value = 'Other';
                });
        } else {
            categoryInput.value = '';
        }
    });

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Real-time validation feedback
    const requiredInputs = form.querySelectorAll('[required]');
    requiredInputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.checkValidity()) {
                this.classList.remove('is-invalid');
                this.classList.add('is-valid');
            } else {
                this.classList.remove('is-valid');
                this.classList.add('is-invalid');
            }
        });
    });
});
</script>
{% endblock %}
