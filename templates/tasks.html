{% extends "base.html" %}

{% block title %}All Tasks - AdhocLog{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="bi bi-list"></i> All Tasks</h1>
            <div>
                <a href="{{ url_for('add_task') }}" class="btn btn-primary">
                    <i class="bi bi-plus"></i> Add Task
                </a>
                <a href="{{ url_for('export_csv', **filters) }}" class="btn btn-success">
                    <i class="bi bi-download"></i> Export CSV
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-funnel"></i> Filters</h5>
            </div>
            <div class="card-body">
                <form method="GET" action="{{ url_for('task_list') }}">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date"
                                   value="{{ filters.get('start_date', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date"
                                   value="{{ filters.get('end_date', '') }}">
                        </div>
                        <div class="col-md-3">
                            <label for="classification" class="form-label">Classification</label>
                            <select class="form-select" id="classification" name="classification">
                                <option value="">All Classifications</option>
                                {% for cls in classifications %}
                                <option value="{{ cls }}" {% if filters.get('classification') == cls %}selected{% endif %}>
                                    {{ cls }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="search" class="form-label">Search</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   placeholder="Title or description..." value="{{ filters.get('search', '') }}">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> Apply Filters
                            </button>
                            <a href="{{ url_for('task_list') }}" class="btn btn-outline-secondary">
                                <i class="bi bi-x-circle"></i> Clear Filters
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Tasks Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-table"></i> Tasks
                    <span class="badge bg-primary">{{ tasks|length }}</span>
                </h5>
            </div>
            <div class="card-body">
                {% if tasks %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Title</th>
                                    <th>Classification</th>
                                    <th>Category</th>
                                    <th>Description</th>
                                    <th>Time (min)</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in tasks %}
                                <tr>
                                    <td>{{ task.date }}</td>
                                    <td><strong>{{ task.title }}</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">{{ task.classification }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ task.category }}</span>
                                    </td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 200px;"
                                              title="{{ task.description }}">
                                            {{ task.description }}
                                        </span>
                                    </td>
                                    <td>{{ task.est_time }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('edit_task', task_id=task.id) }}"
                                               class="btn btn-outline-primary" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-warning"
                                                    onclick="confirmArchive({{ task.id }}, '{{ task.title }}')" title="Archive">
                                                <i class="bi bi-archive"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% include 'pagination.html' %}
                {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-inbox fs-1 text-muted"></i>
                        <p class="text-muted mt-2">No tasks found matching your criteria.</p>
                        <a href="{{ url_for('add_task') }}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> Add Your First Task
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Archive Confirmation Modal -->
<div class="modal fade" id="archiveModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Archive</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to archive the task "<span id="taskTitle"></span>"?</p>
                <p class="text-muted">You can restore it later from the Archive section.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="archiveForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-warning">
                        <i class="bi bi-archive"></i> Archive
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmArchive(taskId, taskTitle) {
    document.getElementById('taskTitle').textContent = taskTitle;
    document.getElementById('archiveForm').action = '/tasks/delete/' + taskId;
    new bootstrap.Modal(document.getElementById('archiveModal')).show();
}
</script>
{% endblock %}
