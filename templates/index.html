{% extends "base.html" %}

{% block title %}Dashboard - AdhocLog{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-2">
            <i class="bi bi-speedometer2"></i> Dashboard
        </h1>
        <p class="text-muted mb-4">Welcome, {{ current_user }}!</p>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center position-relative">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="bi bi-check-circle-fill fs-2 me-3 opacity-75"></i>
                    <div>
                        <h2 class="card-title mb-0 display-4 fw-bold">{{ total_tasks_today }}</h2>
                        <p class="card-text mb-0 opacity-90">Tasks Today</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center position-relative">
                <div class="d-flex align-items-center justify-content-center mb-2">
                    <i class="bi bi-clock-fill fs-2 me-3 opacity-75"></i>
                    <div>
                        <h2 class="card-title mb-0 display-4 fw-bold">{{ total_time_today }}</h2>
                        <p class="card-text mb-0 opacity-90">Minutes Today</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Today's Tasks -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-day"></i> Today's Tasks
                </h5>
                <a href="{{ url_for('add_task') }}" class="btn btn-primary btn-sm">
                    <i class="bi bi-plus"></i> Add Task
                </a>
            </div>
            <div class="card-body">
                {% if today_tasks %}
                    <div class="row">
                        {% for task in today_tasks %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <div class="card task-card h-100">
                                <div class="card-body">
                                    <h6 class="card-title">{{ task.title }}</h6>
                                    <p class="card-text">
                                        <small class="text-muted">
                                            <span class="badge bg-secondary">{{ task.classification }}</span>
                                            <span class="badge bg-info">{{ task.category }}</span>
                                        </small>
                                    </p>
                                    <p class="card-text">{{ task.description[:100] }}{% if task.description|length > 100 %}...{% endif %}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="bi bi-clock"></i> {{ task.est_time }} min
                                        </small>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('edit_task', task_id=task.id) }}" class="btn btn-outline-primary">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-calendar-x fs-1 text-muted"></i>
                        <p class="text-muted mt-2">No tasks for today yet.</p>
                        <a href="{{ url_for('add_task') }}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> Add Your First Task
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Tasks -->
{% if recent_tasks %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="bi bi-clock-history"></i> Recent Tasks (Last 7 Days)
                </h5>
                <a href="{{ url_for('task_list') }}" class="btn btn-outline-primary btn-sm">
                    View All
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Title</th>
                                <th>Classification</th>
                                <th>Time</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in recent_tasks %}
                            <tr>
                                <td>{{ task.date }}</td>
                                <td>{{ task.title }}</td>
                                <td>
                                    <span class="badge bg-secondary">{{ task.classification }}</span>
                                </td>
                                <td>{{ task.est_time }} min</td>
                                <td>
                                    <a href="{{ url_for('edit_task', task_id=task.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
