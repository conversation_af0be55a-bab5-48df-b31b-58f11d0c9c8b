<!-- Pagination Component -->
{% if pagination and pagination.total_pages > 1 %}
<nav aria-label="Page navigation" class="mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div class="text-muted">
            Showing {{ ((pagination.page - 1) * pagination.per_page) + 1 }} to
            {{ pagination.page * pagination.per_page if pagination.page * pagination.per_page <= pagination.total else pagination.total }}
            of {{ pagination.total }} entries
        </div>
        <div class="d-flex align-items-center">
            <label for="per_page" class="form-label me-2 mb-0">Show:</label>
            <select class="form-select form-select-sm" id="per_page" onchange="changePerPage(this.value)" style="width: auto;">
                <option value="10" {% if pagination.per_page == 10 %}selected{% endif %}>10</option>
                <option value="25" {% if pagination.per_page == 25 %}selected{% endif %}>25</option>
                <option value="50" {% if pagination.per_page == 50 %}selected{% endif %}>50</option>
                <option value="100" {% if pagination.per_page == 100 %}selected{% endif %}>100</option>
            </select>
        </div>
    </div>

    <ul class="pagination justify-content-center">
        <!-- First page -->
        {% if pagination.page > 2 %}
        <li class="page-item">
            <a class="page-link" href="{{ build_pagination_url(1) }}">
                <i class="bi bi-chevron-double-left"></i>
            </a>
        </li>
        {% endif %}

        <!-- Previous page -->
        {% if pagination.has_prev %}
        <li class="page-item">
            <a class="page-link" href="{{ build_pagination_url(pagination.prev_page) }}">
                <i class="bi bi-chevron-left"></i>
            </a>
        </li>
        {% else %}
        <li class="page-item disabled">
            <span class="page-link">
                <i class="bi bi-chevron-left"></i>
            </span>
        </li>
        {% endif %}

        <!-- Page numbers -->
        {% set start_page = [pagination.page - 2, 1] | max %}
        {% set end_page = [pagination.page + 2, pagination.total_pages] | min %}

        {% for page_num in range(start_page, end_page + 1) %}
            {% if page_num == pagination.page %}
            <li class="page-item active">
                <span class="page-link">{{ page_num }}</span>
            </li>
            {% else %}
            <li class="page-item">
                <a class="page-link" href="{{ build_pagination_url(page_num) }}">{{ page_num }}</a>
            </li>
            {% endif %}
        {% endfor %}

        <!-- Next page -->
        {% if pagination.has_next %}
        <li class="page-item">
            <a class="page-link" href="{{ build_pagination_url(pagination.next_page) }}">
                <i class="bi bi-chevron-right"></i>
            </a>
        </li>
        {% else %}
        <li class="page-item disabled">
            <span class="page-link">
                <i class="bi bi-chevron-right"></i>
            </span>
        </li>
        {% endif %}

        <!-- Last page -->
        {% if pagination.page < pagination.total_pages - 1 %}
        <li class="page-item">
            <a class="page-link" href="{{ build_pagination_url(pagination.total_pages) }}">
                <i class="bi bi-chevron-double-right"></i>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>

<script>
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.set('page', '1'); // Reset to first page
    window.location.href = url.toString();
}
</script>
{% endif %}
