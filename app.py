from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, make_response
import csv
import io
from datetime import datetime, date
from config import Config
from data_manager import DataManager

app = Flask(__name__)
app.config.from_object(Config)

# Initialize data manager
data_manager = DataManager()

# Template helper function for pagination URLs
@app.template_global()
def build_pagination_url(page):
    """Build pagination URL preserving current filters"""
    args = request.args.copy()
    args['page'] = page
    return url_for(request.endpoint, **args)

@app.route('/')
def dashboard():
    """Dashboard showing today's tasks and recent activity"""
    today = date.today().strftime('%Y-%m-%d')

    # Get today's tasks
    today_result = data_manager.filter_tasks({'start_date': today, 'end_date': today}, per_page=100)
    today_tasks = today_result['tasks']

    # Get recent tasks (last 7 days)
    from datetime import timedelta
    week_ago = (date.today() - timedelta(days=7)).strftime('%Y-%m-%d')
    recent_result = data_manager.filter_tasks({'start_date': week_ago}, per_page=100)
    recent_tasks = recent_result['tasks']

    # Calculate stats
    total_time_today = sum(task.get('est_time', 0) for task in today_tasks)
    total_tasks_today = len(today_tasks)

    return render_template('index.html',
                         today_tasks=today_tasks,
                         recent_tasks=recent_tasks[:10],  # Limit to 10 recent
                         total_time_today=total_time_today,
                         total_tasks_today=total_tasks_today,
                         current_user=data_manager.username)

@app.route('/tasks')
def task_list():
    """Full task list with filtering and pagination"""
    # Get filter parameters
    filters = {
        'start_date': request.args.get('start_date', ''),
        'end_date': request.args.get('end_date', ''),
        'classification': request.args.get('classification', ''),
        'search': request.args.get('search', '')
    }

    # Get pagination parameters
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 10))

    # Remove empty filters
    clean_filters = {k: v for k, v in filters.items() if v}

    # Get filtered tasks with pagination
    result = data_manager.filter_tasks(clean_filters, page=page, per_page=per_page)

    return render_template('tasks.html',
                         tasks=result['tasks'],
                         pagination=result['pagination'],
                         filters=filters,
                         classifications=app.config['CLASSIFICATIONS'],
                         current_user=data_manager.username)

@app.route('/tasks/add', methods=['GET', 'POST'])
def add_task():
    """Add a new task"""
    if request.method == 'POST':
        task_data = {
            'title': request.form.get('title', '').strip(),
            'classification': request.form.get('classification', ''),
            'description': request.form.get('description', '').strip(),
            'est_time': int(request.form.get('est_time', 0)),
            'date': request.form.get('date', date.today().strftime('%Y-%m-%d'))
        }

        # Validate required fields
        if not task_data['title']:
            flash('Title is required', 'error')
            return render_template('add_task.html',
                                 classifications=app.config['CLASSIFICATIONS'],
                                 task_data=task_data,
                                 current_user=data_manager.username)

        try:
            new_task = data_manager.add_task(task_data)
            flash(f'Task "{new_task["title"]}" added successfully!', 'success')
            return redirect(url_for('task_list'))
        except Exception as e:
            flash(f'Error adding task: {str(e)}', 'error')

    return render_template('add_task.html',
                         classifications=app.config['CLASSIFICATIONS'],
                         task_data={},
                         current_user=data_manager.username)

@app.route('/tasks/edit/<int:task_id>', methods=['GET', 'POST'])
def edit_task(task_id):
    """Edit an existing task"""
    task = data_manager.get_task_by_id(task_id)
    if not task:
        flash('Task not found', 'error')
        return redirect(url_for('task_list'))

    if request.method == 'POST':
        task_data = {
            'title': request.form.get('title', '').strip(),
            'classification': request.form.get('classification', ''),
            'description': request.form.get('description', '').strip(),
            'est_time': int(request.form.get('est_time', 0)),
            'date': request.form.get('date', task.get('date', ''))
        }

        # Validate required fields
        if not task_data['title']:
            flash('Title is required', 'error')
            return render_template('edit_task.html',
                                 task=task,
                                 classifications=app.config['CLASSIFICATIONS'],
                                 current_user=data_manager.username)

        try:
            updated_task = data_manager.update_task(task_id, task_data)
            if updated_task:
                flash(f'Task "{updated_task["title"]}" updated successfully!', 'success')
                return redirect(url_for('task_list'))
            else:
                flash('Error updating task', 'error')
        except Exception as e:
            flash(f'Error updating task: {str(e)}', 'error')

    return render_template('edit_task.html',
                         task=task,
                         classifications=app.config['CLASSIFICATIONS'],
                         current_user=data_manager.username)

@app.route('/tasks/delete/<int:task_id>', methods=['POST'])
def delete_task(task_id):
    """Delete a task"""
    task = data_manager.get_task_by_id(task_id)
    if not task:
        flash('Task not found', 'error')
        return redirect(url_for('task_list'))

    try:
        if data_manager.delete_task(task_id):
            flash(f'Task "{task["title"]}" deleted successfully!', 'success')
        else:
            flash('Error deleting task', 'error')
    except Exception as e:
        flash(f'Error deleting task: {str(e)}', 'error')

    return redirect(url_for('task_list'))

@app.route('/archive')
def archive_list():
    """View archived tasks with pagination"""
    page = int(request.args.get('page', 1))
    per_page = int(request.args.get('per_page', 10))

    result = data_manager.get_archived_tasks_paginated(page=page, per_page=per_page)

    return render_template('archive.html',
                         archived_tasks=result['tasks'],
                         pagination=result['pagination'],
                         current_user=data_manager.username)

@app.route('/tasks/restore/<int:task_id>', methods=['POST'])
def restore_task(task_id):
    """Restore a task from archive"""
    archived_tasks = data_manager.get_archived_tasks()
    task = None
    for t in archived_tasks:
        if t.get('id') == task_id:
            task = t
            break

    if not task:
        flash('Archived task not found', 'error')
        return redirect(url_for('archive_list'))

    try:
        if data_manager.restore_task(task_id):
            flash(f'Task "{task["title"]}" restored successfully!', 'success')
        else:
            flash('Error restoring task', 'error')
    except Exception as e:
        flash(f'Error restoring task: {str(e)}', 'error')

    return redirect(url_for('archive_list'))

@app.route('/tasks/permanent_delete/<int:task_id>', methods=['POST'])
def permanent_delete_task(task_id):
    """Permanently delete a task from archive"""
    archived_tasks = data_manager.get_archived_tasks()
    task = None
    for t in archived_tasks:
        if t.get('id') == task_id:
            task = t
            break

    if not task:
        flash('Archived task not found', 'error')
        return redirect(url_for('archive_list'))

    try:
        if data_manager.permanently_delete_task(task_id):
            flash(f'Task "{task["title"]}" permanently deleted!', 'success')
        else:
            flash('Error permanently deleting task', 'error')
    except Exception as e:
        flash(f'Error permanently deleting task: {str(e)}', 'error')

    return redirect(url_for('archive_list'))

@app.route('/export')
def export_csv():
    """Export filtered tasks as CSV"""
    # Get same filters as task list
    filters = {
        'start_date': request.args.get('start_date', ''),
        'end_date': request.args.get('end_date', ''),
        'classification': request.args.get('classification', ''),
        'search': request.args.get('search', '')
    }

    # Remove empty filters
    filters = {k: v for k, v in filters.items() if v}

    # Get filtered tasks
    if filters:
        tasks = data_manager.filter_tasks(filters)
    else:
        tasks = data_manager.get_all_tasks()

    # Sort by date
    tasks.sort(key=lambda x: x.get('date', ''), reverse=True)

    # Create CSV
    output = io.StringIO()
    writer = csv.writer(output)

    # Write header
    writer.writerow(['Date', 'Team Member', 'Task Title', 'Classification', 'Actions Taken / Description', 'Estimated Time (minute)', 'Category'])

    # Write data
    for task in tasks:
        writer.writerow([
            task.get('date', ''),
            task.get('team_member', ''),
            task.get('title', ''),
            task.get('classification', ''),
            task.get('description', ''),
            task.get('est_time', 0),
            task.get('category', '')
        ])

    # Create response
    response = make_response(output.getvalue())
    response.headers['Content-Type'] = 'text/csv'
    response.headers['Content-Disposition'] = f'attachment; filename=tasks_export_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'

    return response

@app.route('/api/get_category/<classification>')
def get_category(classification):
    """API endpoint to get category for a classification"""
    category = app.config['CLASSIFICATION_MAPPING'].get(classification, 'Other')
    return jsonify({'category': category})

@app.route('/api/get_recent_tasks')
def get_recent_tasks():
    """API endpoint to get recent unique tasks for quick add"""
    limit = int(request.args.get('limit', 10))
    all_tasks = data_manager.get_all_tasks()

    # Get unique tasks based on title and classification (most recent first)
    unique_tasks = {}
    for task in sorted(all_tasks, key=lambda x: x.get('date', ''), reverse=True):
        key = (task.get('title', ''), task.get('classification', ''))
        if key not in unique_tasks:
            unique_tasks[key] = {
                'title': task.get('title', ''),
                'classification': task.get('classification', ''),
                'category': task.get('category', ''),
                'description': task.get('description', ''),
                'est_time': task.get('est_time', 30)
            }

    # Return limited number of most recent unique tasks
    recent_tasks = list(unique_tasks.values())[:limit]
    return jsonify({'tasks': recent_tasks})

@app.errorhandler(404)
def not_found(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

if __name__ == '__main__':
    print("Starting Flask application...")
    print("Access the app at: http://127.0.0.1:8000")
    print("Or try: http://localhost:8000")
    print("Press Ctrl+C to stop the server")
    print("-" * 50)

    try:
        app.run(debug=True, host='0.0.0.0', port=8000)
    except Exception as e:
        print(f"Error starting Flask app on port 8000: {e}")
        print("Trying port 5001...")
        try:
            print("Access the app at: http://127.0.0.1:5001")
            app.run(debug=True, host='0.0.0.0', port=5001)
        except Exception as e2:
            print(f"Port 5001 also failed: {e2}")
            print("Please check which ports are available or disable AirPlay Receiver in System Preferences -> Sharing")
